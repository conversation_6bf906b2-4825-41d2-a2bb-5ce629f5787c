<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>从傅里叶变换到DCT：完整原理详解</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .formula {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            text-align: center;
            margin: 15px 0;
            font-size: 18px;
        }
        .demo-area {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px solid #bdc3c7;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px;
            display: block;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover {
            background: #2980b9;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .explanation {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #27ae60;
            margin: 15px 0;
        }
        .warning {
            background: #fdf2e9;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #f39c12;
            margin: 15px 0;
        }
        .math-insight {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            margin: 15px 0;
        }
        input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }
        .slider-container {
            margin: 10px 0;
            text-align: center;
        }
        .step-nav {
            text-align: center;
            margin: 30px 0;
        }
        .step-nav button {
            background: #95a5a6;
            margin: 0 5px;
        }
        .step-nav button.active {
            background: #3498db;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #bdc3c7;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background: #ecf0f1;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>从傅里叶变换到DCT：完整原理详解</h1>
        
        <div class="section">
            <h2>学习路线图</h2>
            <div class="explanation">
                <h3>我们将按以下步骤深入学习：</h3>
                <ol>
                    <li><strong>第0步：数学基础</strong> - 复数、欧拉公式、正交性</li>
                    <li><strong>第1步：频率概念</strong> - 什么是频率？为什么重要？</li>
                    <li><strong>第2步：傅里叶级数</strong> - 周期信号的频率分解</li>
                    <li><strong>第3步：傅里叶变换</strong> - 连续信号的频域表示</li>
                    <li><strong>第4步：离散傅里叶变换(DFT)</strong> - 数字信号处理的核心</li>
                    <li><strong>第5步：从DFT到DCT</strong> - 为什么需要DCT？</li>
                    <li><strong>第6步：DCT详解</strong> - 数学原理与基函数</li>
                    <li><strong>第7步：图像DCT应用</strong> - 二维DCT与图像处理</li>
                    <li><strong>第8步：JPEG压缩</strong> - 实际应用案例</li>
                </ol>
            </div>
        </div>

        <div class="step-nav">
            <button onclick="showStep(0)" class="active" id="step0">第0步</button>
            <button onclick="showStep(1)" id="step1">第1步</button>
            <button onclick="showStep(2)" id="step2">第2步</button>
            <button onclick="showStep(3)" id="step3">第3步</button>
            <button onclick="showStep(4)" id="step4">第4步</button>
            <button onclick="showStep(5)" id="step5">第5步</button>
            <button onclick="showStep(6)" id="step6">第6步</button>
            <button onclick="showStep(7)" id="step7">第7步</button>
            <button onclick="showStep(8)" id="step8">第8步</button>
        </div>

        <!-- 第0步：数学基础 -->
        <div class="section" id="content0">
            <h2>第0步：数学基础</h2>
            
            <h3>1. 复数基础</h3>
            <div class="math-insight">
                <p><strong>复数的表示方法：</strong></p>
                <ul>
                    <li><strong>直角坐标形式</strong>：z = a + bi</li>
                    <li><strong>极坐标形式</strong>：z = r·e^(iθ) = r(cosθ + i·sinθ)</li>
                </ul>
                <p>其中：r = |z| = √(a² + b²)，θ = arctan(b/a)</p>
            </div>

            <div class="demo-area">
                <h3>互动演示：复数可视化</h3>
                <canvas id="complexCanvas" width="400" height="400"></canvas>
                <div class="controls">
                    <div class="slider-container">
                        <label>实部 a: </label>
                        <input type="range" id="realSlider" min="-5" max="5" value="3" step="0.1">
                        <span id="realValue">3</span>
                    </div>
                    <div class="slider-container">
                        <label>虚部 b: </label>
                        <input type="range" id="imagSlider" min="-5" max="5" value="4" step="0.1">
                        <span id="imagValue">4</span>
                    </div>
                </div>
                <p id="complexInfo">z = 3 + 4i = 5·e^(i·0.93) = 5·(cos(0.93) + i·sin(0.93))</p>
            </div>

            <h3>2. 欧拉公式</h3>
            <div class="formula">
                e^(iθ) = cos(θ) + i·sin(θ)
            </div>
            <div class="math-insight">
                <p><strong>欧拉公式的意义：</strong></p>
                <ul>
                    <li>连接了指数函数与三角函数</li>
                    <li>复数的指数形式实际上描述了单位圆上的旋转</li>
                    <li>是傅里叶变换的数学基础</li>
                </ul>
            </div>

            <div class="demo-area">
                <h3>互动演示：欧拉公式可视化</h3>
                <canvas id="eulerCanvas" width="600" height="300"></canvas>
                <div class="controls">
                    <div class="slider-container">
                        <label>角度 θ: </label>
                        <input type="range" id="angleSlider" min="0" max="6.28" value="1.57" step="0.1">
                        <span id="angleValue">1.57</span> 弧度
                    </div>
                    <button onclick="animateEuler()">动画演示</button>
                </div>
            </div>

            <h3>3. 正交性概念</h3>
            <div class="math-insight">
                <p><strong>正交性的定义：</strong></p>
                <p>两个函数f(x)和g(x)在区间[a,b]上正交，当且仅当：</p>
                <div class="formula">∫[a to b] f(x)·g(x) dx = 0</div>
                <p><strong>为什么正交性重要？</strong></p>
                <ul>
                    <li>正交函数系可以作为"基函数"来表示任意函数</li>
                    <li>类似于3D空间中的x、y、z轴互相垂直</li>
                    <li>保证了变换的唯一性和可逆性</li>
                </ul>
            </div>
        </div>

        <!-- 第1步：频率概念 -->
        <div class="section" id="content1" style="display:none;">
            <h2>第1步：频率概念深入理解</h2>

            <h3>1. 什么是频率？</h3>
            <div class="explanation">
                <p><strong>频率</strong>是描述周期性现象快慢的物理量：</p>
                <ul>
                    <li><strong>定义</strong>：单位时间内重复的次数</li>
                    <li><strong>单位</strong>：赫兹(Hz) = 次/秒</li>
                    <li><strong>周期关系</strong>：频率 = 1/周期，f = 1/T</li>
                </ul>
            </div>

            <h3>2. 生活中的频率例子</h3>
            <div class="math-insight">
                <table class="comparison-table">
                    <tr>
                        <th>现象</th>
                        <th>频率范围</th>
                        <th>特征</th>
                    </tr>
                    <tr>
                        <td>人类听觉</td>
                        <td>20Hz - 20kHz</td>
                        <td>低频=低音，高频=高音</td>
                    </tr>
                    <tr>
                        <td>可见光</td>
                        <td>430-750 THz</td>
                        <td>低频=红光，高频=紫光</td>
                    </tr>
                    <tr>
                        <td>心跳</td>
                        <td>1-2 Hz</td>
                        <td>生命体征的重要指标</td>
                    </tr>
                    <tr>
                        <td>交流电</td>
                        <td>50/60 Hz</td>
                        <td>家用电力系统标准</td>
                    </tr>
                </table>
            </div>

            <h3>3. 信号中的频率</h3>
            <div class="demo-area">
                <h3>互动演示：不同频率的正弦波</h3>
                <canvas id="frequencyCanvas" width="800" height="400"></canvas>
                <div class="controls">
                    <div class="slider-container">
                        <label>频率1: </label>
                        <input type="range" id="freq1Slider" min="0.5" max="5" value="1" step="0.1">
                        <span id="freq1Value">1</span> Hz
                    </div>
                    <div class="slider-container">
                        <label>频率2: </label>
                        <input type="range" id="freq2Slider" min="0.5" max="5" value="2" step="0.1">
                        <span id="freq2Value">2</span> Hz
                    </div>
                    <div class="slider-container">
                        <label>频率3: </label>
                        <input type="range" id="freq3Slider" min="0.5" max="5" value="3" step="0.1">
                        <span id="freq3Value">3</span> Hz
                    </div>
                    <button onclick="showCombined()">显示叠加信号</button>
                    <button onclick="showSeparate()">显示分离信号</button>
                </div>
            </div>

            <h3>4. 频率的重要性</h3>
            <div class="warning">
                <p><strong>为什么频率分析如此重要？</strong></p>
                <ul>
                    <li><strong>信号特征识别</strong>：不同频率成分代表不同的信息</li>
                    <li><strong>噪声滤除</strong>：可以去除特定频率的干扰</li>
                    <li><strong>数据压缩</strong>：去除人眼/耳不敏感的频率成分</li>
                    <li><strong>系统分析</strong>：了解系统对不同频率的响应</li>
                </ul>
            </div>

            <h3>5. 图像中的频率概念</h3>
            <div class="math-insight">
                <p><strong>图像的频率特征：</strong></p>
                <ul>
                    <li><strong>低频</strong>：图像的整体亮度、大致轮廓、平滑区域</li>
                    <li><strong>中频</strong>：物体的边缘、纹理的主要特征</li>
                    <li><strong>高频</strong>：细节纹理、噪声、尖锐边缘</li>
                </ul>
            </div>

            <div class="demo-area">
                <h3>互动演示：图像频率成分</h3>
                <canvas id="imageFreqCanvas" width="600" height="200"></canvas>
                <div class="controls">
                    <button onclick="showLowFreq()">显示低频成分</button>
                    <button onclick="showMidFreq()">显示中频成分</button>
                    <button onclick="showHighFreq()">显示高频成分</button>
                    <button onclick="showAllFreq()">显示完整图像</button>
                </div>
                <p>观察不同频率成分如何构成完整图像</p>
            </div>
        </div>

        <div class="section" id="content2" style="display:none;">
            <h2>第2步：傅里叶级数 - 周期信号的频率分解</h2>

            <h3>1. 傅里叶级数的核心思想</h3>
            <div class="explanation">
                <p><strong>任何周期函数都可以表示为正弦波和余弦波的无穷级数：</strong></p>
                <div class="formula">
                    f(t) = a₀/2 + Σ[n=1 to ∞] (aₙcos(nω₀t) + bₙsin(nω₀t))
                </div>
                <p>其中：</p>
                <ul>
                    <li><strong>a₀</strong>：直流分量（平均值）</li>
                    <li><strong>ω₀ = 2π/T</strong>：基频（基本频率）</li>
                    <li><strong>aₙ, bₙ</strong>：各次谐波的幅度系数</li>
                </ul>
            </div>

            <h3>2. 系数计算公式</h3>
            <div class="math-insight">
                <p><strong>傅里叶系数的计算：</strong></p>
                <div class="formula">
                    a₀ = (2/T) ∫[0 to T] f(t) dt<br><br>
                    aₙ = (2/T) ∫[0 to T] f(t)cos(nω₀t) dt<br><br>
                    bₙ = (2/T) ∫[0 to T] f(t)sin(nω₀t) dt
                </div>
                <p>这些公式利用了三角函数的<strong>正交性</strong>！</p>
            </div>

            <h3>3. 复数形式的傅里叶级数</h3>
            <div class="math-insight">
                <p>利用欧拉公式，可以写成更简洁的复数形式：</p>
                <div class="formula">
                    f(t) = Σ[n=-∞ to ∞] cₙe^(inω₀t)
                </div>
                <p>其中：</p>
                <div class="formula">
                    cₙ = (1/T) ∫[0 to T] f(t)e^(-inω₀t) dt
                </div>
            </div>

            <div class="demo-area">
                <h3>互动演示：方波的傅里叶级数分解</h3>
                <canvas id="fourierSeriesCanvas" width="800" height="500"></canvas>
                <div class="controls">
                    <div class="slider-container">
                        <label>谐波数量: </label>
                        <input type="range" id="harmonicsSlider" min="1" max="20" value="5">
                        <span id="harmonicsValue">5</span>
                    </div>
                    <button onclick="animateFourierSeries()">动画演示</button>
                    <button onclick="showFourierCoefficients()">显示系数</button>
                </div>
                <div id="coefficientsDisplay"></div>
            </div>

            <h3>4. 吉布斯现象</h3>
            <div class="warning">
                <p><strong>吉布斯现象</strong>：在不连续点附近，傅里叶级数会产生约9%的过冲</p>
                <ul>
                    <li>这是有限项级数逼近的固有特性</li>
                    <li>增加更多谐波可以减少振荡，但过冲幅度不变</li>
                    <li>这提示我们需要其他变换方法（如DCT）来处理不连续信号</li>
                </ul>
            </div>

            <h3>5. 频谱的概念</h3>
            <div class="explanation">
                <p><strong>频谱</strong>：信号在频域的表示</p>
                <ul>
                    <li><strong>幅度谱</strong>：各频率成分的幅度 |cₙ|</li>
                    <li><strong>相位谱</strong>：各频率成分的相位 ∠cₙ</li>
                    <li><strong>功率谱</strong>：各频率成分的功率 |cₙ|²</li>
                </ul>
            </div>

            <div class="demo-area">
                <h3>互动演示：不同波形的频谱</h3>
                <canvas id="spectrumCanvas" width="800" height="400"></canvas>
                <div class="controls">
                    <button onclick="showWaveSpectrum('square')">方波频谱</button>
                    <button onclick="showWaveSpectrum('triangle')">三角波频谱</button>
                    <button onclick="showWaveSpectrum('sawtooth')">锯齿波频谱</button>
                    <button onclick="showWaveSpectrum('sine')">正弦波频谱</button>
                </div>
                <p>观察不同波形的频谱特征</p>
            </div>
        </div>

        <div class="section" id="content3" style="display:none;">
            <h2>第3步：傅里叶变换</h2>
            <!-- 内容将在下一部分添加 -->
        </div>

        <div class="section" id="content4" style="display:none;">
            <h2>第4步：离散傅里叶变换(DFT) - 数字信号处理的核心</h2>

            <h3>1. 从连续到离散</h3>
            <div class="explanation">
                <p><strong>为什么需要DFT？</strong></p>
                <ul>
                    <li>计算机只能处理<strong>离散</strong>的数字信号</li>
                    <li>实际信号都是<strong>有限长度</strong>的</li>
                    <li>需要<strong>快速算法</strong>进行实时处理</li>
                </ul>
            </div>

            <h3>2. DFT的数学定义</h3>
            <div class="math-insight">
                <p><strong>正变换（时域→频域）：</strong></p>
                <div class="formula">
                    X(k) = Σ[n=0 to N-1] x(n) · e^(-i2πkn/N)
                </div>
                <p><strong>逆变换（频域→时域）：</strong></p>
                <div class="formula">
                    x(n) = (1/N) Σ[k=0 to N-1] X(k) · e^(i2πkn/N)
                </div>
                <p>其中：</p>
                <ul>
                    <li><strong>x(n)</strong>：时域信号，n = 0,1,...,N-1</li>
                    <li><strong>X(k)</strong>：频域信号，k = 0,1,...,N-1</li>
                    <li><strong>N</strong>：信号长度</li>
                </ul>
            </div>

            <h3>3. DFT的物理意义</h3>
            <div class="math-insight">
                <p><strong>DFT做了什么？</strong></p>
                <ul>
                    <li><strong>X(0)</strong>：直流分量（平均值）</li>
                    <li><strong>X(k)</strong>：频率为 k·fs/N 的复正弦波的幅度和相位</li>
                    <li><strong>fs</strong>：采样频率</li>
                    <li><strong>频率分辨率</strong>：Δf = fs/N</li>
                </ul>
            </div>

            <div class="demo-area">
                <h3>互动演示：DFT基函数可视化</h3>
                <canvas id="dftBasisCanvas" width="800" height="400"></canvas>
                <div class="controls">
                    <div class="slider-container">
                        <label>DFT基函数索引 k: </label>
                        <input type="range" id="dftBasisSlider" min="0" max="7" value="0">
                        <span id="dftBasisValue">0</span>
                    </div>
                    <div class="slider-container">
                        <label>信号长度 N: </label>
                        <input type="range" id="dftLengthSlider" min="8" max="32" value="8">
                        <span id="dftLengthValue">8</span>
                    </div>
                </div>
                <p>DFT基函数：e^(-i2πkn/N) = cos(2πkn/N) - i·sin(2πkn/N)</p>
            </div>

            <h3>4. DFT的频率采样特性</h3>
            <div class="warning">
                <p><strong>关键理解：DFT是频率域的均匀采样</strong></p>
                <ul>
                    <li>DFT在频率轴上均匀采样N个点</li>
                    <li>采样频率：fₖ = k·fs/N，k = 0,1,...,N-1</li>
                    <li>覆盖范围：[0, fs)，即完整的奈奎斯特频率范围</li>
                    <li>由于对称性，通常只关注前N/2个点</li>
                </ul>
            </div>

            <div class="demo-area">
                <h3>互动演示：DFT频率采样</h3>
                <canvas id="dftSamplingCanvas" width="800" height="300"></canvas>
                <div class="controls">
                    <div class="slider-container">
                        <label>采样点数 N: </label>
                        <input type="range" id="samplingNSlider" min="8" max="64" value="16">
                        <span id="samplingNValue">16</span>
                    </div>
                    <div class="slider-container">
                        <label>采样频率 fs: </label>
                        <input type="range" id="samplingFsSlider" min="100" max="1000" value="500" step="50">
                        <span id="samplingFsValue">500</span> Hz
                    </div>
                </div>
                <p>频率分辨率 = fs/N = <span id="freqResolution">31.25</span> Hz</p>
            </div>

            <h3>5. DFT实例计算</h3>
            <div class="demo-area">
                <h3>互动演示：4点DFT手工计算</h3>
                <canvas id="dft4PointCanvas" width="800" height="500"></canvas>
                <div class="controls">
                    <button onclick="stepByStepDFT()">逐步计算DFT</button>
                    <button onclick="showDFTMatrix()">显示DFT矩阵</button>
                    <button onclick="resetDFTDemo()">重置</button>
                </div>
                <div id="dftCalculation"></div>
            </div>
        </div>

        <div class="section" id="content5" style="display:none;">
            <h2>第5步：从DFT到DCT - 关键的演进</h2>

            <h3>1. DFT的局限性</h3>
            <div class="warning">
                <p><strong>DFT在实际应用中的问题：</strong></p>
                <ul>
                    <li><strong>复数运算</strong>：需要处理实部和虚部，计算复杂</li>
                    <li><strong>边界效应</strong>：假设信号周期延拓，在边界处可能不连续</li>
                    <li><strong>存储需求</strong>：复数需要双倍存储空间</li>
                    <li><strong>能量分散</strong>：能量分布在所有频率上</li>
                </ul>
            </div>

            <h3>2. DCT的设计动机</h3>
            <div class="explanation">
                <p><strong>DCT如何解决DFT的问题：</strong></p>
                <ul>
                    <li><strong>实数变换</strong>：只使用余弦函数，避免复数</li>
                    <li><strong>偶对称延拓</strong>：假设信号关于边界偶对称，保证连续性</li>
                    <li><strong>能量集中</strong>：大部分能量集中在低频系数</li>
                    <li><strong>适合图像</strong>：图像通常在局部区域相对平滑</li>
                </ul>
            </div>

            <h3>3. 对称延拓的关键思想</h3>
            <div class="demo-area">
                <h3>互动演示：DFT vs DCT的边界处理</h3>
                <canvas id="boundaryCanvas" width="800" height="400"></canvas>
                <div class="controls">
                    <button onclick="showPeriodicExtension()">DFT：周期延拓</button>
                    <button onclick="showEvenExtension()">DCT：偶对称延拓</button>
                    <button onclick="showOriginalSignal()">原始信号</button>
                </div>
                <p>观察两种延拓方式对边界连续性的影响</p>
            </div>

            <h3>4. 频率范围的差异</h3>
            <div class="math-insight">
                <p><strong>频率覆盖范围对比：</strong></p>
                <table class="comparison-table">
                    <tr>
                        <th>变换</th>
                        <th>频率范围</th>
                        <th>基函数</th>
                        <th>系数数量</th>
                    </tr>
                    <tr>
                        <td>DFT</td>
                        <td>[0, 2π)</td>
                        <td>e^(i2πkn/N)</td>
                        <td>N个复数</td>
                    </tr>
                    <tr>
                        <td>DCT</td>
                        <td>[0, π/2)</td>
                        <td>cos(π(2n+1)k/(2N))</td>
                        <td>N个实数</td>
                    </tr>
                </table>
            </div>

            <div class="demo-area">
                <h3>互动演示：DFT vs DCT基函数对比</h3>
                <canvas id="basisComparisonCanvas" width="800" height="500"></canvas>
                <div class="controls">
                    <div class="slider-container">
                        <label>基函数索引: </label>
                        <input type="range" id="basisIndexSlider" min="0" max="7" value="1">
                        <span id="basisIndexValue">1</span>
                    </div>
                    <button onclick="showDFTBasis()">显示DFT基函数</button>
                    <button onclick="showDCTBasis()">显示DCT基函数</button>
                    <button onclick="showBothBasis()">同时显示</button>
                </div>
            </div>

            <h3>5. 能量集中特性</h3>
            <div class="explanation">
                <p><strong>为什么DCT能量更集中？</strong></p>
                <ul>
                    <li><strong>平滑信号</strong>：自然图像局部通常平滑，低频成分占主导</li>
                    <li><strong>边界连续</strong>：偶对称延拓避免了人工不连续</li>
                    <li><strong>最优性</strong>：对于马尔可夫过程，DCT接近KL变换的性能</li>
                </ul>
            </div>

            <div class="demo-area">
                <h3>互动演示：能量集中度对比</h3>
                <canvas id="energyComparisonCanvas" width="800" height="400"></canvas>
                <div class="controls">
                    <button onclick="compareEnergyConcentration()">比较能量集中度</button>
                    <button onclick="showCumulativeEnergy()">显示累积能量</button>
                </div>
                <p>观察DFT和DCT在处理同一信号时的能量分布差异</p>
            </div>

            <h3>6. 计算复杂度对比</h3>
            <div class="math-insight">
                <p><strong>计算复杂度分析：</strong></p>
                <table class="comparison-table">
                    <tr>
                        <th>算法</th>
                        <th>直接计算</th>
                        <th>快速算法</th>
                        <th>存储需求</th>
                    </tr>
                    <tr>
                        <td>DFT</td>
                        <td>O(N²) 复数乘法</td>
                        <td>O(N log N) FFT</td>
                        <td>2N (实部+虚部)</td>
                    </tr>
                    <tr>
                        <td>DCT</td>
                        <td>O(N²) 实数乘法</td>
                        <td>O(N log N) 快速DCT</td>
                        <td>N (仅实数)</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="section" id="content6" style="display:none;">
            <h2>第6步：DCT详解</h2>
            <!-- 内容将在下一部分添加 -->
        </div>

        <div class="section" id="content7" style="display:none;">
            <h2>第7步：图像DCT应用</h2>
            <!-- 内容将在下一部分添加 -->
        </div>

        <div class="section" id="content8" style="display:none;">
            <h2>第8步：JPEG压缩</h2>
            <!-- 内容将在下一部分添加 -->
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 0;
        let animationId;

        // 初始化
        window.onload = function() {
            initComplexDemo();
            initEulerDemo();
            initFrequencyDemo();
            initBoundaryDemo();
            initBasisComparisonDemo();
            showStep(0);
        };

        // 步骤导航
        function showStep(step) {
            // 隐藏所有内容
            for (let i = 0; i <= 8; i++) {
                const content = document.getElementById(`content${i}`);
                const button = document.getElementById(`step${i}`);
                if (content) content.style.display = 'none';
                if (button) button.classList.remove('active');
            }
            
            // 显示当前步骤
            const currentContent = document.getElementById(`content${step}`);
            const currentButton = document.getElementById(`step${step}`);
            if (currentContent) currentContent.style.display = 'block';
            if (currentButton) currentButton.classList.add('active');
            
            currentStep = step;
        }

        // 复数演示
        function initComplexDemo() {
            const canvas = document.getElementById('complexCanvas');
            const ctx = canvas.getContext('2d');
            const realSlider = document.getElementById('realSlider');
            const imagSlider = document.getElementById('imagSlider');
            const realValue = document.getElementById('realValue');
            const imagValue = document.getElementById('imagValue');
            const complexInfo = document.getElementById('complexInfo');

            function drawComplex() {
                const a = parseFloat(realSlider.value);
                const b = parseFloat(imagSlider.value);

                realValue.textContent = a.toFixed(1);
                imagValue.textContent = b.toFixed(1);

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const scale = 40;

                // 绘制坐标轴
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 1;
                ctx.beginPath();
                // 实轴
                ctx.moveTo(0, centerY);
                ctx.lineTo(canvas.width, centerY);
                // 虚轴
                ctx.moveTo(centerX, 0);
                ctx.lineTo(centerX, canvas.height);
                ctx.stroke();

                // 绘制网格
                ctx.strokeStyle = '#ecf0f1';
                for (let i = -5; i <= 5; i++) {
                    if (i !== 0) {
                        // 垂直线
                        ctx.beginPath();
                        ctx.moveTo(centerX + i * scale, 0);
                        ctx.lineTo(centerX + i * scale, canvas.height);
                        ctx.stroke();
                        // 水平线
                        ctx.beginPath();
                        ctx.moveTo(0, centerY + i * scale);
                        ctx.lineTo(canvas.width, centerY + i * scale);
                        ctx.stroke();
                    }
                }

                // 绘制复数点
                const x = centerX + a * scale;
                const y = centerY - b * scale;

                ctx.fillStyle = '#e74c3c';
                ctx.beginPath();
                ctx.arc(x, y, 8, 0, 2 * Math.PI);
                ctx.fill();

                // 绘制向量
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(x, y);
                ctx.stroke();

                // 绘制实部和虚部投影
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                // 实部投影
                ctx.beginPath();
                ctx.moveTo(x, centerY);
                ctx.lineTo(x, y);
                ctx.stroke();
                // 虚部投影
                ctx.beginPath();
                ctx.moveTo(centerX, y);
                ctx.lineTo(x, y);
                ctx.stroke();
                ctx.setLineDash([]);

                // 计算极坐标形式
                const r = Math.sqrt(a * a + b * b);
                const theta = Math.atan2(b, a);

                // 添加标签
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Arial';
                ctx.fillText('实轴 (Real)', centerX + 5 * scale + 10, centerY - 5);
                ctx.fillText('虚轴 (Imaginary)', centerX + 5, 20);
                ctx.fillText(`(${a.toFixed(1)}, ${b.toFixed(1)}i)`, x + 10, y - 10);

                // 更新信息
                complexInfo.textContent =
                    `z = ${a.toFixed(1)} + ${b.toFixed(1)}i = ${r.toFixed(2)}·e^(i·${theta.toFixed(2)}) = ${r.toFixed(2)}·(cos(${theta.toFixed(2)}) + i·sin(${theta.toFixed(2)}))`;
            }

            realSlider.addEventListener('input', drawComplex);
            imagSlider.addEventListener('input', drawComplex);
            drawComplex();
        }

        // 欧拉公式演示
        function initEulerDemo() {
            const canvas = document.getElementById('eulerCanvas');
            const ctx = canvas.getContext('2d');
            const angleSlider = document.getElementById('angleSlider');
            const angleValue = document.getElementById('angleValue');

            function drawEuler() {
                const theta = parseFloat(angleSlider.value);
                angleValue.textContent = theta.toFixed(2);

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                const centerX = 150;
                const centerY = 150;
                const radius = 100;

                // 左侧：单位圆
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                ctx.stroke();

                // 坐标轴
                ctx.beginPath();
                ctx.moveTo(centerX - radius - 20, centerY);
                ctx.lineTo(centerX + radius + 20, centerY);
                ctx.moveTo(centerX, centerY - radius - 20);
                ctx.lineTo(centerX, centerY + radius + 20);
                ctx.stroke();

                // 角度线
                const x = centerX + radius * Math.cos(theta);
                const y = centerY - radius * Math.sin(theta);

                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(x, y);
                ctx.stroke();

                // 点
                ctx.fillStyle = '#e74c3c';
                ctx.beginPath();
                ctx.arc(x, y, 6, 0, 2 * Math.PI);
                ctx.fill();

                // cos和sin投影
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 2;
                ctx.setLineDash([3, 3]);
                ctx.beginPath();
                ctx.moveTo(x, centerY);
                ctx.lineTo(x, y);
                ctx.stroke();
                ctx.beginPath();
                ctx.moveTo(centerX, y);
                ctx.lineTo(x, y);
                ctx.stroke();
                ctx.setLineDash([]);

                // 右侧：波形图
                const waveStartX = 350;
                const waveY = 150;
                const waveWidth = 200;

                // 绘制cos波形
                ctx.strokeStyle = '#27ae60';
                ctx.lineWidth = 2;
                ctx.beginPath();
                for (let i = 0; i <= waveWidth; i++) {
                    const t = i / waveWidth * 4 * Math.PI;
                    const cosY = waveY - 50 * Math.cos(t);
                    if (i === 0) {
                        ctx.moveTo(waveStartX + i, cosY);
                    } else {
                        ctx.lineTo(waveStartX + i, cosY);
                    }
                }
                ctx.stroke();

                // 绘制sin波形
                ctx.strokeStyle = '#f39c12';
                ctx.lineWidth = 2;
                ctx.beginPath();
                for (let i = 0; i <= waveWidth; i++) {
                    const t = i / waveWidth * 4 * Math.PI;
                    const sinY = waveY - 50 * Math.sin(t);
                    if (i === 0) {
                        ctx.moveTo(waveStartX + i, sinY);
                    } else {
                        ctx.lineTo(waveStartX + i, sinY);
                    }
                }
                ctx.stroke();

                // 当前位置标记
                const currentX = waveStartX + (theta / (2 * Math.PI)) * (waveWidth / 2);
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(currentX, waveY - 80);
                ctx.lineTo(currentX, waveY + 80);
                ctx.stroke();

                // 标签
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Arial';
                ctx.fillText('单位圆', centerX - 30, 30);
                ctx.fillText('cos(θ) + i·sin(θ)', waveStartX, 30);
                ctx.fillText(`θ = ${theta.toFixed(2)}`, centerX - 30, 280);
                ctx.fillText(`cos(${theta.toFixed(2)}) = ${Math.cos(theta).toFixed(2)}`, waveStartX, 50);
                ctx.fillText(`sin(${theta.toFixed(2)}) = ${Math.sin(theta).toFixed(2)}`, waveStartX, 70);

                ctx.fillStyle = '#27ae60';
                ctx.fillText('cos(θ)', waveStartX + 160, 90);
                ctx.fillStyle = '#f39c12';
                ctx.fillText('sin(θ)', waveStartX + 160, 110);
            }

            angleSlider.addEventListener('input', drawEuler);
            drawEuler();
        }

        // 欧拉公式动画
        function animateEuler() {
            const angleSlider = document.getElementById('angleSlider');
            let angle = 0;

            function animate() {
                angle += 0.05;
                if (angle > 2 * Math.PI) angle = 0;

                angleSlider.value = angle;
                angleSlider.dispatchEvent(new Event('input'));

                animationId = requestAnimationFrame(animate);
            }

            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            } else {
                animate();
            }
        }

        // 频率演示
        function initFrequencyDemo() {
            // 这里可以添加频率演示的初始化代码
        }

        function showCombined() {
            const canvas = document.getElementById('frequencyCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');

            const freq1 = parseFloat(document.getElementById('freq1Slider').value);
            const freq2 = parseFloat(document.getElementById('freq2Slider').value);
            const freq3 = parseFloat(document.getElementById('freq3Slider').value);

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制坐标轴
            ctx.strokeStyle = '#bdc3c7';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(0, canvas.height/2);
            ctx.lineTo(canvas.width, canvas.height/2);
            ctx.stroke();

            // 绘制叠加信号
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let x = 0; x < canvas.width; x++) {
                const t = x / canvas.width * 4 * Math.PI;
                const y1 = 30 * Math.sin(freq1 * t);
                const y2 = 20 * Math.sin(freq2 * t);
                const y3 = 15 * Math.sin(freq3 * t);
                const y = canvas.height/2 - (y1 + y2 + y3);

                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();

            // 添加标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.fillText(`叠加信号: ${freq1}Hz + ${freq2}Hz + ${freq3}Hz`, 10, 30);
        }

        function showSeparate() {
            const canvas = document.getElementById('frequencyCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');

            const freq1 = parseFloat(document.getElementById('freq1Slider').value);
            const freq2 = parseFloat(document.getElementById('freq2Slider').value);
            const freq3 = parseFloat(document.getElementById('freq3Slider').value);

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const colors = ['#e74c3c', '#f39c12', '#27ae60'];
            const freqs = [freq1, freq2, freq3];
            const amplitudes = [30, 20, 15];

            for (let i = 0; i < 3; i++) {
                ctx.strokeStyle = colors[i];
                ctx.lineWidth = 2;
                ctx.beginPath();

                const offsetY = (i - 1) * 120 + canvas.height/2;

                for (let x = 0; x < canvas.width; x++) {
                    const t = x / canvas.width * 4 * Math.PI;
                    const y = offsetY - amplitudes[i] * Math.sin(freqs[i] * t);

                    if (x === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();

                // 添加标签
                ctx.fillStyle = colors[i];
                ctx.font = '14px Arial';
                ctx.fillText(`${freqs[i]}Hz`, 10, offsetY - 40);
            }
        }

        // 边界处理演示
        function initBoundaryDemo() {
            // 初始化边界演示
        }

        function showPeriodicExtension() {
            const canvas = document.getElementById('boundaryCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 原始信号（一个周期）
            const signal = [];
            const N = 64;
            for (let i = 0; i < N; i++) {
                signal[i] = 50 * Math.sin(2 * Math.PI * i / N) + 30 * Math.sin(6 * Math.PI * i / N);
            }

            // 绘制多个周期（周期延拓）
            ctx.strokeStyle = '#3498db';
            ctx.lineWidth = 2;
            ctx.beginPath();

            const periods = 3;
            for (let p = 0; p < periods; p++) {
                for (let i = 0; i < N; i++) {
                    const x = (p * N + i) * canvas.width / (periods * N);
                    const y = canvas.height/2 - signal[i];

                    if (p === 0 && i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
            }
            ctx.stroke();

            // 标记边界
            for (let p = 1; p < periods; p++) {
                const x = p * canvas.width / periods;
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
                ctx.setLineDash([]);
            }

            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.fillText('DFT: 周期延拓 - 注意边界处的不连续', 10, 30);
        }

        function showEvenExtension() {
            const canvas = document.getElementById('boundaryCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 原始信号
            const signal = [];
            const N = 32;
            for (let i = 0; i < N; i++) {
                signal[i] = 50 * Math.sin(Math.PI * i / N) + 20 * Math.sin(3 * Math.PI * i / N);
            }

            // 偶对称延拓
            ctx.strokeStyle = '#27ae60';
            ctx.lineWidth = 2;
            ctx.beginPath();

            // 原始部分
            for (let i = 0; i < N; i++) {
                const x = i * canvas.width / (2 * N);
                const y = canvas.height/2 - signal[i];

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }

            // 镜像部分
            for (let i = N-1; i >= 0; i--) {
                const x = (N + (N-1-i)) * canvas.width / (2 * N);
                const y = canvas.height/2 - signal[i];
                ctx.lineTo(x, y);
            }
            ctx.stroke();

            // 标记中心
            const centerX = canvas.width / 2;
            ctx.strokeStyle = '#f39c12';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(centerX, 0);
            ctx.lineTo(centerX, canvas.height);
            ctx.stroke();
            ctx.setLineDash([]);

            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.fillText('DCT: 偶对称延拓 - 边界处完全连续', 10, 30);
        }

        function showOriginalSignal() {
            const canvas = document.getElementById('boundaryCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 原始信号
            const signal = [];
            const N = 64;
            for (let i = 0; i < N; i++) {
                signal[i] = 50 * Math.sin(2 * Math.PI * i / N) + 30 * Math.sin(6 * Math.PI * i / N);
            }

            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let i = 0; i < N; i++) {
                const x = i * canvas.width / N;
                const y = canvas.height/2 - signal[i];

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();

            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.fillText('原始有限长度信号', 10, 30);
        }

        // 基函数对比演示
        function initBasisComparisonDemo() {
            // 初始化基函数对比演示
        }

        function showDFTBasis() {
            const canvas = document.getElementById('basisComparisonCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');

            const k = parseInt(document.getElementById('basisIndexSlider').value);
            const N = 16;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制DFT基函数的实部和虚部
            const colors = ['#e74c3c', '#3498db'];
            const labels = ['cos(2πkn/N)', '-sin(2πkn/N)'];

            for (let component = 0; component < 2; component++) {
                ctx.strokeStyle = colors[component];
                ctx.lineWidth = 2;
                ctx.beginPath();

                const offsetY = component * 120 + 100;

                for (let n = 0; n < N; n++) {
                    const x = n * canvas.width / N;
                    let y;
                    if (component === 0) {
                        y = offsetY - 50 * Math.cos(2 * Math.PI * k * n / N);
                    } else {
                        y = offsetY - 50 * (-Math.sin(2 * Math.PI * k * n / N));
                    }

                    if (n === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }

                    // 绘制采样点
                    ctx.fillStyle = colors[component];
                    ctx.beginPath();
                    ctx.arc(x, y, 3, 0, 2 * Math.PI);
                    ctx.fill();
                }
                ctx.stroke();

                // 添加标签
                ctx.fillStyle = colors[component];
                ctx.font = '14px Arial';
                ctx.fillText(labels[component], 10, offsetY - 60);
            }

            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.fillText(`DFT基函数 k=${k}: e^(-i2πkn/N)`, 10, 30);
        }

        function showDCTBasis() {
            const canvas = document.getElementById('basisComparisonCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');

            const k = parseInt(document.getElementById('basisIndexSlider').value);
            const N = 16;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制DCT基函数
            ctx.strokeStyle = '#27ae60';
            ctx.lineWidth = 3;
            ctx.beginPath();

            const alpha = k === 0 ? 1/Math.sqrt(N) : Math.sqrt(2/N);

            for (let n = 0; n < N; n++) {
                const x = n * canvas.width / N;
                const y = canvas.height/2 - 80 * alpha * Math.cos(Math.PI * k * (2*n + 1) / (2*N));

                if (n === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }

                // 绘制采样点
                ctx.fillStyle = '#27ae60';
                ctx.beginPath();
                ctx.arc(x, y, 4, 0, 2 * Math.PI);
                ctx.fill();
            }
            ctx.stroke();

            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.fillText(`DCT基函数 k=${k}: cos(π(2n+1)k/(2N))`, 10, 30);
            ctx.fillText(`归一化系数 α=${alpha.toFixed(3)}`, 10, 50);
        }

        function showBothBasis() {
            showDFTBasis();
            showDCTBasis();
        }
    </script>
</body>
</html>
