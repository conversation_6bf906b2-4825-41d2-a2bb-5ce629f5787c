<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>傅里叶变换与DCT原理详解</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .formula {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            text-align: center;
            margin: 15px 0;
            font-size: 18px;
        }
        .demo-area {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px solid #bdc3c7;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px;
            display: block;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover {
            background: #2980b9;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .explanation {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #27ae60;
            margin: 15px 0;
        }
        .warning {
            background: #fdf2e9;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #f39c12;
            margin: 15px 0;
        }
        input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }
        .slider-container {
            margin: 10px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>傅里叶变换与DCT原理详解</h1>
        
        <div class="section">
            <h2>第一步：什么是频率？</h2>
            <p>在开始学习傅里叶变换之前，我们需要理解<strong>频率</strong>的概念。</p>
            
            <div class="explanation">
                <h3>生活中的频率例子：</h3>
                <ul>
                    <li><strong>音乐</strong>：高音（高频）vs 低音（低频）</li>
                    <li><strong>光线</strong>：红光（低频）vs 蓝光（高频）</li>
                    <li><strong>图像</strong>：细节变化快（高频）vs 平滑区域（低频）</li>
                </ul>
            </div>

            <div class="demo-area">
                <h3>互动演示：不同频率的波形</h3>
                <canvas id="waveCanvas" width="800" height="200"></canvas>
                <div class="controls">
                    <div class="slider-container">
                        <label>频率: </label>
                        <input type="range" id="frequencySlider" min="1" max="10" value="2" step="0.5">
                        <span id="frequencyValue">2</span> Hz
                    </div>
                    <div class="slider-container">
                        <label>振幅: </label>
                        <input type="range" id="amplitudeSlider" min="10" max="80" value="50">
                        <span id="amplitudeValue">50</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>第二步：傅里叶变换的核心思想</h2>
            
            <div class="explanation">
                <h3>核心理念：任何复杂的信号都可以分解为简单正弦波的组合</h3>
                <p>就像任何颜色都可以用红、绿、蓝三种基本颜色混合得到一样，任何复杂的波形都可以用不同频率的正弦波叠加得到。</p>
            </div>

            <div class="formula">
                傅里叶变换公式：<br>
                F(ω) = ∫ f(t) × e^(-iωt) dt
            </div>

            <div class="warning">
                <strong>不要被公式吓到！</strong>这个公式的本质就是：<br>
                "测量原始信号f(t)中包含多少频率为ω的成分"
            </div>

            <div class="demo-area">
                <h3>互动演示：信号分解</h3>
                <canvas id="decompositionCanvas" width="800" height="400"></canvas>
                <div class="controls">
                    <button onclick="showOriginal()">显示原始信号</button>
                    <button onclick="showComponents()">显示分解成分</button>
                    <button onclick="showReconstruction()">显示重构过程</button>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>第三步：从傅里叶变换到DCT</h2>
            
            <div class="explanation">
                <h3>为什么需要DCT（离散余弦变换）？</h3>
                <ul>
                    <li><strong>实数运算</strong>：DCT只涉及实数，计算更简单</li>
                    <li><strong>能量集中</strong>：大部分能量集中在低频部分</li>
                    <li><strong>适合图像</strong>：图像通常在边界处连续，DCT处理效果更好</li>
                </ul>
            </div>

            <div class="formula">
                一维DCT公式：<br>
                X(k) = α(k) × Σ[n=0 to N-1] x(n) × cos(π×k×(2n+1)/(2N))
            </div>

            <div class="demo-area">
                <h3>互动演示：DCT基函数</h3>
                <canvas id="dctBasisCanvas" width="800" height="300"></canvas>
                <div class="controls">
                    <div class="slider-container">
                        <label>DCT基函数索引: </label>
                        <input type="range" id="dctBasisSlider" min="0" max="7" value="0">
                        <span id="dctBasisValue">0</span>
                    </div>
                </div>
                <p>DCT基函数就像是不同"频率"的余弦波，用来分解信号</p>
            </div>
        </div>

        <div class="section">
            <h2>第四步：图像上的DCT应用</h2>

            <div class="explanation">
                <h3>图像DCT的工作原理：</h3>
                <ol>
                    <li><strong>分块</strong>：将图像分成8×8的小块</li>
                    <li><strong>DCT变换</strong>：对每个8×8块进行二维DCT</li>
                    <li><strong>频率分析</strong>：得到频率域表示</li>
                    <li><strong>压缩</strong>：去除高频成分（JPEG压缩原理）</li>
                </ol>
            </div>

            <div class="formula">
                二维DCT公式：<br>
                F(u,v) = α(u)α(v) × ΣΣ f(x,y) × cos(π×u×(2x+1)/16) × cos(π×v×(2y+1)/16)
            </div>

            <div class="demo-area">
                <h3>互动演示：图像DCT变换</h3>
                <div style="display: flex; justify-content: space-around; flex-wrap: wrap;">
                    <div>
                        <h4>原始图像</h4>
                        <canvas id="originalImageCanvas" width="256" height="256"></canvas>
                    </div>
                    <div>
                        <h4>DCT频谱</h4>
                        <canvas id="dctSpectrumCanvas" width="256" height="256"></canvas>
                    </div>
                    <div>
                        <h4>重构图像</h4>
                        <canvas id="reconstructedCanvas" width="256" height="256"></canvas>
                    </div>
                </div>

                <div class="controls">
                    <button onclick="generateTestImage()">生成测试图像</button>
                    <button onclick="performDCT()">执行DCT变换</button>
                    <button onclick="performIDCT()">执行逆DCT</button>
                </div>

                <div class="controls">
                    <div class="slider-container">
                        <label>保留系数比例: </label>
                        <input type="range" id="compressionSlider" min="0.1" max="1" value="1" step="0.1">
                        <span id="compressionValue">100</span>%
                    </div>
                    <button onclick="applyCompression()">应用压缩</button>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>第五步：实际应用 - JPEG压缩</h2>

            <div class="explanation">
                <h3>JPEG压缩如何利用DCT：</h3>
                <ol>
                    <li><strong>DCT变换</strong>：将图像从空间域转换到频率域</li>
                    <li><strong>量化</strong>：根据人眼视觉特性，减少高频信息</li>
                    <li><strong>编码</strong>：对量化后的系数进行熵编码</li>
                </ol>

                <p><strong>关键洞察</strong>：人眼对低频信息（整体亮度、大致形状）比高频信息（细节纹理）更敏感，所以可以安全地丢弃部分高频信息。</p>
            </div>

            <div class="demo-area">
                <h3>互动演示：压缩效果对比</h3>
                <canvas id="compressionDemo" width="800" height="400"></canvas>
                <div class="controls">
                    <div class="slider-container">
                        <label>压缩质量: </label>
                        <input type="range" id="qualitySlider" min="10" max="100" value="80">
                        <span id="qualityValue">80</span>%
                    </div>
                    <button onclick="updateCompression()">更新压缩效果</button>
                </div>
                <p id="compressionInfo">文件大小减少: 0%</p>
            </div>
        </div>

        <div class="section">
            <h2>总结</h2>
            <div class="explanation">
                <h3>学习要点回顾：</h3>
                <ol>
                    <li><strong>频率概念</strong>：理解信号中的快变化（高频）和慢变化（低频）</li>
                    <li><strong>傅里叶变换</strong>：将复杂信号分解为简单正弦波的组合</li>
                    <li><strong>DCT优势</strong>：实数运算、能量集中、适合图像处理</li>
                    <li><strong>图像应用</strong>：8×8分块、二维DCT、压缩应用</li>
                    <li><strong>实际意义</strong>：JPEG、视频编码等现代技术的基础</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let animationId;
        let dctCoefficients = [];
        let originalImageData = [];

        // 初始化所有演示
        window.onload = function() {
            initWaveDemo();
            initDecompositionDemo();
            initDCTBasisDemo();
            initImageDemo();
        };

        // 波形演示
        function initWaveDemo() {
            const canvas = document.getElementById('waveCanvas');
            const ctx = canvas.getContext('2d');
            const frequencySlider = document.getElementById('frequencySlider');
            const amplitudeSlider = document.getElementById('amplitudeSlider');
            const frequencyValue = document.getElementById('frequencyValue');
            const amplitudeValue = document.getElementById('amplitudeValue');

            function drawWave() {
                const frequency = parseFloat(frequencySlider.value);
                const amplitude = parseFloat(amplitudeSlider.value);

                frequencyValue.textContent = frequency;
                amplitudeValue.textContent = amplitude;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制坐标轴
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(0, canvas.height/2);
                ctx.lineTo(canvas.width, canvas.height/2);
                ctx.stroke();

                // 绘制波形
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 3;
                ctx.beginPath();

                for (let x = 0; x < canvas.width; x++) {
                    const t = x / canvas.width * 4 * Math.PI; // 4个周期
                    const y = canvas.height/2 - amplitude * Math.sin(frequency * t);

                    if (x === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();

                // 添加标签
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Arial';
                ctx.fillText(`频率: ${frequency} Hz`, 10, 20);
                ctx.fillText(`振幅: ${amplitude}`, 10, 40);
            }

            frequencySlider.addEventListener('input', drawWave);
            amplitudeSlider.addEventListener('input', drawWave);
            drawWave();
        }

        // 信号分解演示
        function initDecompositionDemo() {
            const canvas = document.getElementById('decompositionCanvas');
            const ctx = canvas.getContext('2d');

            // 创建一个复合信号：低频 + 中频 + 高频
            const signal = [];
            const N = 200;
            for (let i = 0; i < N; i++) {
                const t = i / N * 4 * Math.PI;
                signal[i] = 30 * Math.sin(t) + 20 * Math.sin(3*t) + 10 * Math.sin(7*t);
            }

            window.originalSignal = signal;
            window.components = [
                signal.map((_, i) => 30 * Math.sin(i / N * 4 * Math.PI)),
                signal.map((_, i) => 20 * Math.sin(3 * i / N * 4 * Math.PI)),
                signal.map((_, i) => 10 * Math.sin(7 * i / N * 4 * Math.PI))
            ];
        }

        function showOriginal() {
            const canvas = document.getElementById('decompositionCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            drawSignal(ctx, window.originalSignal, '#2c3e50', '原始复合信号');
        }

        function showComponents() {
            const canvas = document.getElementById('decompositionCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const colors = ['#e74c3c', '#f39c12', '#27ae60'];
            const labels = ['低频成分 (1Hz)', '中频成分 (3Hz)', '高频成分 (7Hz)'];

            window.components.forEach((component, i) => {
                drawSignal(ctx, component, colors[i], labels[i], i * 100);
            });
        }

        function showReconstruction() {
            const canvas = document.getElementById('decompositionCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 逐步重构
            let reconstruction = new Array(window.originalSignal.length).fill(0);
            const colors = ['#e74c3c', '#f39c12', '#27ae60', '#2c3e50'];
            const labels = ['+ 低频', '+ 中频', '+ 高频', '= 原始信号'];

            for (let i = 0; i < window.components.length; i++) {
                for (let j = 0; j < reconstruction.length; j++) {
                    reconstruction[j] += window.components[i][j];
                }
                drawSignal(ctx, [...reconstruction], colors[i], labels[i], i * 100);
            }
        }

        function drawSignal(ctx, signal, color, label, offsetY = 0) {
            const canvas = ctx.canvas;
            const centerY = canvas.height/2 + offsetY;

            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let i = 0; i < signal.length; i++) {
                const x = i * canvas.width / signal.length;
                const y = centerY - signal[i];

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();

            // 添加标签
            ctx.fillStyle = color;
            ctx.font = '12px Arial';
            ctx.fillText(label, 10, centerY - 60);
        }

        // DCT基函数演示
        function initDCTBasisDemo() {
            const canvas = document.getElementById('dctBasisCanvas');
            const ctx = canvas.getContext('2d');
            const slider = document.getElementById('dctBasisSlider');
            const valueSpan = document.getElementById('dctBasisValue');

            function drawDCTBasis() {
                const k = parseInt(slider.value);
                valueSpan.textContent = k;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制坐标轴
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(0, canvas.height/2);
                ctx.lineTo(canvas.width, canvas.height/2);
                ctx.stroke();

                // 绘制DCT基函数
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 3;
                ctx.beginPath();

                const N = 8; // DCT块大小
                const amplitude = 80;

                for (let x = 0; x < canvas.width; x++) {
                    const n = x / canvas.width * N;
                    const alpha = k === 0 ? 1/Math.sqrt(N) : Math.sqrt(2/N);
                    const y = canvas.height/2 - amplitude * alpha * Math.cos(Math.PI * k * (2*n + 1) / (2*N));

                    if (x === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();

                // 添加标签
                ctx.fillStyle = '#2c3e50';
                ctx.font = '16px Arial';
                ctx.fillText(`DCT基函数 ${k} ${k === 0 ? '(DC分量)' : '(AC分量)'}`, 10, 30);

                // 添加频率说明
                const freqText = k === 0 ? '直流分量 - 平均值' : `频率分量 ${k} - ${k === 1 ? '低频' : k <= 3 ? '中频' : '高频'}`;
                ctx.fillText(freqText, 10, 50);
            }

            slider.addEventListener('input', drawDCTBasis);
            drawDCTBasis();
        }

        // 图像DCT演示
        function initImageDemo() {
            generateTestImage();
        }

        function generateTestImage() {
            const canvas = document.getElementById('originalImageCanvas');
            const ctx = canvas.getContext('2d');
            const size = 256;

            // 创建测试图像：渐变 + 一些几何形状
            const imageData = ctx.createImageData(size, size);
            const data = imageData.data;

            for (let y = 0; y < size; y++) {
                for (let x = 0; x < size; x++) {
                    const idx = (y * size + x) * 4;

                    // 基础渐变
                    let value = (x + y) / (2 * size) * 255;

                    // 添加一些几何形状
                    const centerX = size / 2;
                    const centerY = size / 2;
                    const dist = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);

                    if (dist < 50) {
                        value = 255; // 白色圆
                    } else if (x > size * 0.7 && y < size * 0.3) {
                        value = 0; // 黑色矩形
                    }

                    data[idx] = value;     // R
                    data[idx + 1] = value; // G
                    data[idx + 2] = value; // B
                    data[idx + 3] = 255;   // A
                }
            }

            ctx.putImageData(imageData, 0, 0);

            // 保存原始图像数据
            originalImageData = [];
            for (let i = 0; i < data.length; i += 4) {
                originalImageData.push(data[i]);
            }
        }

        function performDCT() {
            if (originalImageData.length === 0) {
                alert('请先生成测试图像');
                return;
            }

            const size = 256;
            const blockSize = 8;
            dctCoefficients = [];

            // 对每个8x8块执行DCT
            for (let by = 0; by < size; by += blockSize) {
                for (let bx = 0; bx < size; bx += blockSize) {
                    const block = [];

                    // 提取8x8块
                    for (let y = 0; y < blockSize; y++) {
                        for (let x = 0; x < blockSize; x++) {
                            const idx = (by + y) * size + (bx + x);
                            block.push(originalImageData[idx] - 128); // 中心化
                        }
                    }

                    // 执行2D DCT
                    const dctBlock = dct2d(block, blockSize);
                    dctCoefficients.push(dctBlock);
                }
            }

            // 显示DCT频谱
            displayDCTSpectrum();
        }

        // 2D DCT变换
        function dct2d(block, size) {
            const result = new Array(size * size);

            for (let u = 0; u < size; u++) {
                for (let v = 0; v < size; v++) {
                    let sum = 0;
                    const alphaU = u === 0 ? 1/Math.sqrt(size) : Math.sqrt(2/size);
                    const alphaV = v === 0 ? 1/Math.sqrt(size) : Math.sqrt(2/size);

                    for (let x = 0; x < size; x++) {
                        for (let y = 0; y < size; y++) {
                            const cosU = Math.cos(Math.PI * u * (2*x + 1) / (2*size));
                            const cosV = Math.cos(Math.PI * v * (2*y + 1) / (2*size));
                            sum += block[y * size + x] * cosU * cosV;
                        }
                    }

                    result[v * size + u] = alphaU * alphaV * sum;
                }
            }

            return result;
        }

        // 2D 逆DCT变换
        function idct2d(dctBlock, size) {
            const result = new Array(size * size);

            for (let x = 0; x < size; x++) {
                for (let y = 0; y < size; y++) {
                    let sum = 0;

                    for (let u = 0; u < size; u++) {
                        for (let v = 0; v < size; v++) {
                            const alphaU = u === 0 ? 1/Math.sqrt(size) : Math.sqrt(2/size);
                            const alphaV = v === 0 ? 1/Math.sqrt(size) : Math.sqrt(2/size);
                            const cosU = Math.cos(Math.PI * u * (2*x + 1) / (2*size));
                            const cosV = Math.cos(Math.PI * v * (2*y + 1) / (2*size));
                            sum += alphaU * alphaV * dctBlock[v * size + u] * cosU * cosV;
                        }
                    }

                    result[y * size + x] = sum + 128; // 去中心化
                }
            }

            return result;
        }

        function displayDCTSpectrum() {
            const canvas = document.getElementById('dctSpectrumCanvas');
            const ctx = canvas.getContext('2d');
            const size = 256;
            const blockSize = 8;

            const imageData = ctx.createImageData(size, size);
            const data = imageData.data;

            let blockIndex = 0;
            for (let by = 0; by < size; by += blockSize) {
                for (let bx = 0; bx < size; bx += blockSize) {
                    const dctBlock = dctCoefficients[blockIndex++];

                    // 找到最大值用于归一化
                    const maxVal = Math.max(...dctBlock.map(Math.abs));

                    for (let y = 0; y < blockSize; y++) {
                        for (let x = 0; x < blockSize; x++) {
                            const idx = ((by + y) * size + (bx + x)) * 4;
                            const dctIdx = y * blockSize + x;

                            // 归一化到0-255范围，取对数增强可视化
                            const normalized = Math.abs(dctBlock[dctIdx]) / maxVal;
                            const logVal = Math.log(1 + normalized * 255) / Math.log(256) * 255;

                            data[idx] = logVal;     // R
                            data[idx + 1] = logVal; // G
                            data[idx + 2] = logVal; // B
                            data[idx + 3] = 255;    // A
                        }
                    }
                }
            }

            ctx.putImageData(imageData, 0, 0);
        }

        function performIDCT() {
            if (dctCoefficients.length === 0) {
                alert('请先执行DCT变换');
                return;
            }

            const canvas = document.getElementById('reconstructedCanvas');
            const ctx = canvas.getContext('2d');
            const size = 256;
            const blockSize = 8;

            const imageData = ctx.createImageData(size, size);
            const data = imageData.data;

            let blockIndex = 0;
            for (let by = 0; by < size; by += blockSize) {
                for (let bx = 0; bx < size; bx += blockSize) {
                    const dctBlock = dctCoefficients[blockIndex++];
                    const reconstructedBlock = idct2d(dctBlock, blockSize);

                    for (let y = 0; y < blockSize; y++) {
                        for (let x = 0; x < blockSize; x++) {
                            const idx = ((by + y) * size + (bx + x)) * 4;
                            const blockIdx = y * blockSize + x;

                            const value = Math.max(0, Math.min(255, reconstructedBlock[blockIdx]));

                            data[idx] = value;     // R
                            data[idx + 1] = value; // G
                            data[idx + 2] = value; // B
                            data[idx + 3] = 255;   // A
                        }
                    }
                }
            }

            ctx.putImageData(imageData, 0, 0);
        }

        function applyCompression() {
            if (dctCoefficients.length === 0) {
                alert('请先执行DCT变换');
                return;
            }

            const compressionRatio = parseFloat(document.getElementById('compressionSlider').value);
            document.getElementById('compressionValue').textContent = Math.round(compressionRatio * 100);

            // 创建压缩后的DCT系数
            const compressedCoefficients = dctCoefficients.map(block => {
                const compressed = [...block];
                const blockSize = 8;
                const keepCount = Math.floor(blockSize * blockSize * compressionRatio);

                // 按重要性排序（左上角的低频系数更重要）
                const indices = [];
                for (let i = 0; i < blockSize * blockSize; i++) {
                    const u = i % blockSize;
                    const v = Math.floor(i / blockSize);
                    indices.push({index: i, importance: u + v, value: Math.abs(compressed[i])});
                }

                // 按重要性和幅值排序
                indices.sort((a, b) => {
                    if (a.importance !== b.importance) return a.importance - b.importance;
                    return b.value - a.value;
                });

                // 保留最重要的系数，其他置零
                for (let i = keepCount; i < indices.length; i++) {
                    compressed[indices[i].index] = 0;
                }

                return compressed;
            });

            // 重构压缩后的图像
            const canvas = document.getElementById('reconstructedCanvas');
            const ctx = canvas.getContext('2d');
            const size = 256;
            const blockSize = 8;

            const imageData = ctx.createImageData(size, size);
            const data = imageData.data;

            let blockIndex = 0;
            for (let by = 0; by < size; by += blockSize) {
                for (let bx = 0; bx < size; bx += blockSize) {
                    const dctBlock = compressedCoefficients[blockIndex++];
                    const reconstructedBlock = idct2d(dctBlock, blockSize);

                    for (let y = 0; y < blockSize; y++) {
                        for (let x = 0; x < blockSize; x++) {
                            const idx = ((by + y) * size + (bx + x)) * 4;
                            const blockIdx = y * blockSize + x;

                            const value = Math.max(0, Math.min(255, reconstructedBlock[blockIdx]));

                            data[idx] = value;     // R
                            data[idx + 1] = value; // G
                            data[idx + 2] = value; // B
                            data[idx + 3] = 255;   // A
                        }
                    }
                }
            }

            ctx.putImageData(imageData, 0, 0);
        }

        function updateCompression() {
            const quality = parseInt(document.getElementById('qualitySlider').value);
            document.getElementById('qualityValue').textContent = quality;

            // 模拟压缩效果
            const compressionRatio = (100 - quality) / 100;
            const fileSizeReduction = Math.round(compressionRatio * 80); // 模拟文件大小减少

            document.getElementById('compressionInfo').textContent =
                `文件大小减少: ${fileSizeReduction}%, 质量: ${quality}%`;

            // 这里可以添加实际的压缩演示代码
            const canvas = document.getElementById('compressionDemo');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制质量对比示意图
            ctx.fillStyle = '#ecf0f1';
            ctx.fillRect(0, 0, canvas.width/2, canvas.height);
            ctx.fillRect(canvas.width/2, 0, canvas.width/2, canvas.height);

            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.fillText('原始图像', 20, 30);
            ctx.fillText(`压缩后 (质量: ${quality}%)`, canvas.width/2 + 20, 30);

            // 绘制模拟的质量效果
            const blockSize = 20;
            for (let y = 50; y < canvas.height - 50; y += blockSize) {
                for (let x = 20; x < canvas.width/2 - 20; x += blockSize) {
                    const brightness = Math.random() * 255;
                    ctx.fillStyle = `rgb(${brightness},${brightness},${brightness})`;
                    ctx.fillRect(x, y, blockSize-2, blockSize-2);
                }
            }

            // 压缩后的效果（模拟块效应）
            const compressedBlockSize = blockSize + (100 - quality) / 10;
            for (let y = 50; y < canvas.height - 50; y += compressedBlockSize) {
                for (let x = canvas.width/2 + 20; x < canvas.width - 20; x += compressedBlockSize) {
                    const brightness = Math.random() * 255;
                    ctx.fillStyle = `rgb(${brightness},${brightness},${brightness})`;
                    ctx.fillRect(x, y, compressedBlockSize-2, compressedBlockSize-2);
                }
            }
        }
    </script>
</body>
</html>

    <script>
        // 全局变量
        let animationId;
        let dctCoefficients = [];
        let originalImageData = [];

        // 初始化所有演示
        window.onload = function() {
            initWaveDemo();
            initDecompositionDemo();
            initDCTBasisDemo();
            initImageDemo();
        };

        // 波形演示
        function initWaveDemo() {
            const canvas = document.getElementById('waveCanvas');
            const ctx = canvas.getContext('2d');
            const frequencySlider = document.getElementById('frequencySlider');
            const amplitudeSlider = document.getElementById('amplitudeSlider');
            const frequencyValue = document.getElementById('frequencyValue');
            const amplitudeValue = document.getElementById('amplitudeValue');

            function drawWave() {
                const frequency = parseFloat(frequencySlider.value);
                const amplitude = parseFloat(amplitudeSlider.value);

                frequencyValue.textContent = frequency;
                amplitudeValue.textContent = amplitude;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制坐标轴
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(0, canvas.height/2);
                ctx.lineTo(canvas.width, canvas.height/2);
                ctx.stroke();

                // 绘制波形
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 3;
                ctx.beginPath();

                for (let x = 0; x < canvas.width; x++) {
                    const t = x / canvas.width * 4 * Math.PI; // 4个周期
                    const y = canvas.height/2 - amplitude * Math.sin(frequency * t);

                    if (x === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();

                // 添加标签
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Arial';
                ctx.fillText(`频率: ${frequency} Hz`, 10, 20);
                ctx.fillText(`振幅: ${amplitude}`, 10, 40);
            }

            frequencySlider.addEventListener('input', drawWave);
            amplitudeSlider.addEventListener('input', drawWave);
            drawWave();
        }

        // 信号分解演示
        function initDecompositionDemo() {
            const canvas = document.getElementById('decompositionCanvas');
            const ctx = canvas.getContext('2d');

            // 创建一个复合信号：低频 + 中频 + 高频
            const signal = [];
            const N = 200;
            for (let i = 0; i < N; i++) {
                const t = i / N * 4 * Math.PI;
                signal[i] = 30 * Math.sin(t) + 20 * Math.sin(3*t) + 10 * Math.sin(7*t);
            }

            window.originalSignal = signal;
            window.components = [
                signal.map((_, i) => 30 * Math.sin(i / N * 4 * Math.PI)),
                signal.map((_, i) => 20 * Math.sin(3 * i / N * 4 * Math.PI)),
                signal.map((_, i) => 10 * Math.sin(7 * i / N * 4 * Math.PI))
            ];
        }

        function showOriginal() {
            const canvas = document.getElementById('decompositionCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            drawSignal(ctx, window.originalSignal, '#2c3e50', '原始复合信号');
        }

        function showComponents() {
            const canvas = document.getElementById('decompositionCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const colors = ['#e74c3c', '#f39c12', '#27ae60'];
            const labels = ['低频成分 (1Hz)', '中频成分 (3Hz)', '高频成分 (7Hz)'];

            window.components.forEach((component, i) => {
                drawSignal(ctx, component, colors[i], labels[i], i * 100);
            });
        }

        function showReconstruction() {
            const canvas = document.getElementById('decompositionCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 逐步重构
            let reconstruction = new Array(window.originalSignal.length).fill(0);
            const colors = ['#e74c3c', '#f39c12', '#27ae60', '#2c3e50'];
            const labels = ['+ 低频', '+ 中频', '+ 高频', '= 原始信号'];

            for (let i = 0; i < window.components.length; i++) {
                for (let j = 0; j < reconstruction.length; j++) {
                    reconstruction[j] += window.components[i][j];
                }
                drawSignal(ctx, [...reconstruction], colors[i], labels[i], i * 100);
            }
        }

        function drawSignal(ctx, signal, color, label, offsetY = 0) {
            const canvas = ctx.canvas;
            const centerY = canvas.height/2 + offsetY;

            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let i = 0; i < signal.length; i++) {
                const x = i * canvas.width / signal.length;
                const y = centerY - signal[i];

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();

            // 添加标签
            ctx.fillStyle = color;
            ctx.font = '12px Arial';
            ctx.fillText(label, 10, centerY - 60);
        }
